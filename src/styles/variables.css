:root {
  /* Colors */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-teal: #00ecf0;
  --color-teal-dark: #00d1ca;
  --color-teal-light: #4dfff0;
  --color-blue: #3d9eee;
  --color-blue-dark: #2a7fd0;
  --color-blue-light: #6bb8ff;
  --color-green: #19fdb2;
  --color-green-dark: #00d7c3;
  --color-purple: #9d4eff;
  --color-pink: #ff4ebd;
  --color-yellow: #ffcb45;
  --color-orange: #ff7846;
  --color-red: #ff4e6e;
  --color-cobalt: #1527ee;
  --color-cobalt-dark: #0029b1;
  --color-azure: #44a3db;
  --color-rose: #be1757;
  --color-rust: #d74a49;

  /* Background colors */
  --bg-dark: #080a10;
  --bg-dark-accent: #0f1118;
  --bg-modern: #15181e;
  --bg-modern-dark: #0e1014;
  --bg-modern-dark-2: #090a0d;
  --bg-blue-0: #0a0c10;
  --bg-blue-1: #0d1117;
  --bg-blue-1-5: #0b1429;
  --bg-surface: rgba(255, 255, 255, 0.03);
  --bg-surface-hover: rgba(255, 255, 255, 0.05);
  --bg-surface-active: rgba(255, 255, 255, 0.08);
  --bg-gauze: rgba(21, 24, 30, 0.7);
  --bg-gauze-heavy: rgba(21, 24, 30, 0.95);

  /* Theme colors */
  --primary: var(--color-teal);
  --primary-dark: var(--color-teal-dark);
  --primary-light: var(--color-teal-light);
  --secondary: var(--color-blue);
  --secondary-dark: var(--color-blue-dark);
  --secondary-light: var(--color-blue-light);
  --accent-green: var(--color-green);
  --accent-purple: var(--color-purple);
  --accent-pink: var(--color-pink);
  --accent-yellow: var(--color-yellow);
  --accent-orange: var(--color-orange);
  --accent-red: var(--color-red);
  --accent-teal: var(--color-green-dark);

  /* Text colors */
  --text-primary: var(--color-white);
  --text-secondary: rgba(255, 255, 255, 0.85);
  --text-tertiary: rgba(255, 255, 255, 0.65);
  --text-disabled: rgba(255, 255, 255, 0.45);
  --text-cw-0: #4b6adb;
  --text-cw-1: #8da0e4;
  --text-cw-2: #b0b3db;
  --text-cw-3: #c4c9cf;
  --text-cw-4: #a5a5a5;
  --text-cw-5: #757575;
  --text-cw-6: #dde0f4;

  /* Borders */
  --border-light: rgba(255, 255, 255, 0.08);
  --border-medium: rgba(255, 255, 255, 0.12);
  --border-focus: rgba(0, 240, 181, 0.5);
  --border-primary: rgba(0, 236, 240, 0.2);
  --border-secondary: rgba(61, 158, 238, 0.2);
  --border-green: rgba(25, 253, 178, 0.2);

  /* Alpha values */
  --alpha-05: 0.05;
  --alpha-08: 0.08;
  --alpha-10: 0.1;
  --alpha-15: 0.15;
  --alpha-20: 0.2;
  --alpha-25: 0.25;
  --alpha-30: 0.3;
  --alpha-35: 0.35;
  --alpha-40: 0.4;
  --alpha-45: 0.45;
  --alpha-50: 0.5;
  --alpha-60: 0.6;
  --alpha-65: 0.65;
  --alpha-70: 0.7;
  --alpha-80: 0.8;
  --alpha-85: 0.85;
  --alpha-90: 0.9;

  /* Gradients */
  --primary-gradient: linear-gradient(135deg, var(--primary), var(--secondary));
  --primary-gradient-alt: linear-gradient(92deg, var(--primary) 0%, var(--primary-light) 100%);
  --secondary-gradient: linear-gradient(135deg, var(--secondary), var(--primary));
  --green-gradient: linear-gradient(135deg, var(--accent-green), var(--accent-teal));
  --purple-gradient: linear-gradient(135deg, var(--accent-purple), var(--accent-pink));
  --yellow-gradient: linear-gradient(135deg, var(--accent-yellow), var(--accent-orange));
  --red-gradient: linear-gradient(135deg, var(--accent-red), var(--accent-pink));
  --blue-gradient: linear-gradient(135deg, var(--text-cw-0), var(--color-azure));

  /* Shadows */
  --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.25);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.35);
  --shadow-lg: 0 8px 40px rgba(0, 0, 0, 0.45);
  --shadow-primary: 0 8px 30px rgba(0, 240, 181, 0.25);
  --shadow-secondary: 0 8px 30px rgba(61, 158, 238, 0.25);
  --shadow-green: 0 8px 30px rgba(25, 253, 178, 0.25);

  /* Glows */
  --glow-primary: 0 0 20px rgba(0, 240, 181, 0.4);
  --glow-secondary: 0 0 20px rgba(61, 158, 238, 0.4);
  --glow-purple: 0 0 20px rgba(157, 78, 255, 0.4);
  --glow-pink: 0 0 20px rgba(255, 78, 189, 0.4);
  --glow-green: 0 0 20px rgba(25, 253, 178, 0.4);

  /* Blurs */
  --blur-sm: blur(4px);
  --blur-md: blur(8px);
  --blur-lg: blur(16px);

  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-display: 'Sora', 'Outfit', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', 'Roboto Mono', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.5rem;
  --text-5xl: 3.25rem;
  --text-6xl: 4rem;
  --text-7xl: 5rem;
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Spacing - Base */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;

  /* Spacing - Section specific */
  --section-spacing-y: 6rem;
  /* Standard vertical padding for sections */
  --section-spacing-y-sm: 4rem;
  /* Smaller vertical padding for compact sections */
  --section-spacing-y-lg: 8rem;
  /* Larger vertical padding for hero/featured sections */
  --section-spacing-gap: 2rem;
  /* Gap between sections when needed */
  --section-heading-spacing: 4rem;
  /* Space between section heading and content */

  /* Spacing - Responsive adjustments */
  --section-spacing-y-tablet: 5rem;
  --section-spacing-y-mobile: 4rem;
  --section-spacing-y-mobile-sm: 3rem;
  --section-heading-spacing-mobile: 2rem;

  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 500ms cubic-bezier(0.34, 1.56, 0.64, 1);

  /* Z-index */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-auto: auto;

  /* Container widths */
  --container-max: 1200px;
  /* Main container max width */
  --container-narrow: 960px;
  /* Narrower container for text-heavy sections */
  --container-text: 720px;
  /* Container for pure text content */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1440px;

  /* Glass effect */
  --glass-bg: rgba(15, 17, 24, 0.7);
  --glass-border: rgba(255, 255, 255, 0.08);
  --glass-highlight: rgba(255, 255, 255, 0.05);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}