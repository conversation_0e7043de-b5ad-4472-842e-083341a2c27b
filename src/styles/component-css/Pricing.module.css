@keyframes fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.pricingSection{padding:var(--space-16)0;position:relative;overflow:hidden}.pricingHeading{text-align:center;margin-bottom:var(--space-10)}.pricingGrid{display:grid;grid-template-columns:1fr 1fr;gap:2rem;margin-top:2rem;width:100%;max-width:var(--container-xl);margin-left:auto;margin-right:auto}.leftPanel,.rightPanel{border:1px solid rgba(255,255,255,.05);border-radius:var(--radius-xl);padding:var(--space-8);display:flex;flex-direction:column;transition:transform var(--transition-normal),box-shadow var(--transition-normal)}.leftPanel{position:relative;background-color:rgba(255,255,255,.03);background-image:url(/images/gradients/g9.webp);background-size:cover;background-repeat:no-repeat;align-items:flex-start;justify-content:end}.rightPanel{background-color:rgba(255,255,255,.02);gap:var(--space-8)}.rightPanel:hover{box-shadow:var(--shadow-lg)}.pricingActions{display:flex;justify-content:space-between;align-items:center;gap:var(--space-4);margin:var(--space-8) auto 0 auto;max-width:var(--container-xl)}.currencyDropdown{position:relative;z-index:10}.currencyMenu,.dropdownTrigger{border:1px solid rgba(255,255,255,.1);border-radius:var(--radius-md)}.dropdownTrigger{background:rgba(255,255,255,.05);padding:var(--space-2) var(--space-4);color:var(--text-secondary);font-size:var(--text-sm);display:inline-flex;align-items:center;gap:var(--space-2);cursor:pointer;transition:all var(--transition-normal)}.dropdownTrigger:hover{background:rgba(255,255,255,.1)}.currencyMenu{position:absolute;top:100%;left:0;width:max-content;background:var(--bg-dark-accent);margin-top:var(--space-2);max-height:200px;overflow-y:auto;box-shadow:var(--shadow-md);z-index:20;animation:fadeIn .2s ease-out}.currencyOption{padding:var(--space-2) var(--space-4);color:var(--text-secondary);background:0 0;border:0;width:100%;text-align:left;cursor:pointer;transition:all var(--transition-fast)}.currencyOption:hover{background:rgba(255,255,255,.05);color:var(--text-primary)}.currencyOption.active{background:rgba(25,253,178,.1);color:var(--primary)}.priceDisplayBox{display:flex;align-items:baseline;gap:var(--space-2);margin:var(--space-8)0}.priceAmount{font-size:var(--text-6xl);font-weight:var(--font-bold);background:linear-gradient(to right,var(--text-primary),var(--text-secondary));background-clip:text;color:transparent}.pricePeriod{font-size:var(--text-xl);color:var(--text-tertiary)}.switchWrapper{display:flex;flex-direction:column;align-items:flex-start;margin-top:var(--space-4)}.switchBody{width:60px;height:30px;background-color:rgba(255,255,255,.1);border-radius:15px;position:relative;cursor:pointer;transition:background-color var(--transition-normal);margin-bottom:var(--space-2)}.switchBody.on{background-color:rgba(25,253,178,.2)}.switchIndicator{width:24px;height:24px;background-color:var(--text-primary);border-radius:50%;position:absolute;top:3px;left:3px;transition:transform var(--transition-normal),background-color var(--transition-normal)}.switchBody.on .switchIndicator{transform:translateX(30px);background-color:var(--primary)}.switchLabel{font-size:var(--text-sm);color:var(--text-secondary);line-height:var(--leading-relaxed)}.featureSection{background-color:rgba(0,0,0,.2);border-radius:var(--radius-lg);padding:var(--space-6);transition:transform var(--transition-normal)}.featureSection:hover{transform:translateY(-2px)}.featureBadge{display:inline-block;padding:var(--space-1) var(--space-4);background-color:rgba(255,255,255,.05);border-radius:var(--radius-full);font-size:var(--text-sm);font-weight:var(--font-medium);color:var(--text-primary);margin-bottom:var(--space-4)}.featureBadge.green{background-color:rgba(25,253,178,.1);color:var(--primary)}.featuresList{list-style:none;padding:0;margin:0;display:flex;flex-direction:column;gap:var(--space-4)}.featureItem{display:flex;align-items:flex-start;gap:var(--space-3);color:var(--text-tertiary);transition:color var(--transition-fast)}.featureItem:hover{color:var(--text-secondary)}.featureIcon{color:var(--primary);font-size:var(--text-base);flex-shrink:0;margin-top:.2rem}.featureText{font-size:var(--text-sm);line-height:var(--leading-relaxed)}.linkLight{color:var(--text-primary);display:inline-flex;align-items:center;text-decoration:none;font-weight:var(--font-medium);transition:color var(--transition-fast);background:0 0;padding:0;border:0}.linkLight:hover{color:var(--primary)}.currencyInfoIcon{margin-left:1em}@media (max-width:1024px){.pricingGrid{gap:var(--space-6)}.leftPanel,.rightPanel{padding:var(--space-6)}.priceAmount{font-size:var(--text-4xl)}}@media (max-width:768px){.pricingGrid{grid-template-columns:1fr}.leftPanel,.rightPanel{padding:var(--space-4)}.pricingActions{flex-direction:column;gap:var(--space-6)}}@media (max-width:480px){.priceAmount{font-size:var(--text-3xl)}.pricePeriod{font-size:var(--text-base)}.featureSection{padding:var(--space-4)}}