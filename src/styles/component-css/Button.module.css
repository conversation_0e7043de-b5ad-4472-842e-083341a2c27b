.button, .buttonContent {
  position: relative;
  align-items: center
}

.button {
  display: inline-flex;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-medium);
  font-size: var(--text-base);
  transition: all .25s cubic-bezier(.25, .46, .45, .94);
  cursor: pointer;
  border: 0;
  overflow: hidden;
  text-decoration: none;
  letter-spacing: var(--tracking-wide);
  backdrop-filter: var(--blur-sm);
  z-index: 1
}

.button::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0)0, rgba(255, 255, 255, var(--alpha-05)) 25%, rgba(255, 255, 255, var(--alpha-20)) 50%, rgba(255, 255, 255, var(--alpha-05)) 75%, rgba(255, 255, 255, 0) 100%);
  z-index: 1;
  transition: left .7s ease;
  pointer-events: none
}

.button:hover::after {
  left: 100%
}

.button::before {
  content: "";
  position: absolute;
  inset: 0;
  z-index: -1;
  opacity: 0;
  transition: opacity .3s ease;
  border-radius: inherit
}

.buttonContent {
  z-index: 2;
  display: flex;
  gap: 8px
}

.primary {
  background: var(--primary-gradient-alt);
  color: var(--bg-dark);
  box-shadow: 0 4px 15px rgba(var(--color-green), var(--alpha-20)), inset 0 1px 0 rgba(255, 255, 255, var(--alpha-20));
  text-shadow: 0 1px 1px rgba(0, 0, 0, var(--alpha-10))
}

.primary::before {
  background: linear-gradient(92deg, var(--primary-light) 0%, var(--primary) 100%);
  box-shadow: 0 8px 25px rgba(var(--color-green), var(--alpha-40))
}

.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(var(--color-green), var(--alpha-30)), inset 0 1px 0 rgba(255, 255, 255, var(--alpha-30))
}

.hero:hover::before, .primary:hover::before, .secondary:hover::before {
  opacity: 1
}

.primary:active {
  transform: translateY(0);
  box-shadow: 0 5px 15px rgba(var(--color-green), var(--alpha-20)), inset 0 1px 0 rgba(255, 255, 255, var(--alpha-20));
  transition: all .1s ease
}

.secondary {
  background: rgba(30, 30, 35, var(--alpha-20));
  color: var(--text-primary);
  border: 1px solid var(--border-light);
  box-shadow: 0 4px 12px rgba(0, 0, 0, var(--alpha-10)), inset 0 1px 0 rgba(255, 255, 255, var(--alpha-05));
  backdrop-filter: var(--blur-md)
}

.secondary::before {
  background: rgba(40, 40, 45, var(--alpha-30));
  border: 1px solid var(--border-medium);
  box-shadow: 0 8px 20px rgba(0, 0, 0, var(--alpha-15))
}

.secondary:hover {
  transform: translateY(-3px);
  border-color: var(--border-green);
  box-shadow: 0 8px 20px rgba(0, 0, 0, var(--alpha-15)), 0 0 15px rgba(var(--color-green), var(--alpha-10)), inset 0 1px 0 rgba(255, 255, 255, var(--alpha-10))
}

.secondary:active {
  transform: translateY(0);
  box-shadow: 0 4px 10px rgba(0, 0, 0, var(--alpha-10)), inset 0 1px 0 rgba(255, 255, 255, var(--alpha-05));
  transition: all .1s ease
}

.hero {
  background: var(--primary-gradient);
  color: var(--bg-dark);
  font-weight: var(--font-semibold);
  padding: var(--space-4) var(--space-8);
  box-shadow: 0 8px 25px rgba(var(--color-green), var(--alpha-25)), inset 0 1px 0 rgba(255, 255, 255, var(--alpha-30));
  border-radius: var(--radius-xl);
  letter-spacing: var(--tracking-wide)
}

.hero::before {
  background: var(--secondary-gradient);
  box-shadow: 0 12px 30px rgba(var(--color-green), var(--alpha-40))
}

.hero:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(var(--color-green), var(--alpha-35)), inset 0 1px 0 rgba(255, 255, 255, var(--alpha-40))
}

.hero:active, .text:hover {
  transform: translateY(-1px)
}

.hero:active {
  box-shadow: 0 8px 20px rgba(var(--color-green), var(--alpha-30)), inset 0 1px 0 rgba(255, 255, 255, var(--alpha-30));
  transition: all .1s ease
}

.text {
  background: 0 0;
  color: var(--text-secondary);
  padding: var(--space-2);
  backdrop-filter: none
}

.text:hover {
  color: var(--primary)
}

.text:active {
  transform: translateY(0)
}

.large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl)
}

.small {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  border-radius: var(--radius-md)
}

.fullWidth {
  width: 100%
}

.button i {
  font-size: 1.2em;
  transition: transform .2s ease
}

.button:hover i {
  transform: translateX(3px)
}

@media (max-width:768px) {
  .button {
    padding: var(--space-3) var(--space-5)
  }

  .large {
    padding: var(--space-3) var(--space-6)
  }
}

@media (max-width:480px) {
  .button {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm)
  }

  .large {
    padding: var(--space-3) var(--space-5);
    font-size: var(--text-base)
  }
}