/* 
 * Section Spacing - Standardized spacing between landing page sections
 * This file provides consistent spacing between adjacent sections
 */

/* Default section spacing - all sections have standard padding from globals.css */

/* Hero section - special case with no top padding */
.heroSection {
  padding-top: 0;
  padding-bottom: var(--section-spacing-y);
  margin-bottom: 0;
}

/* Features section - comes right after hero */
.featuresSection {
  padding-top: var(--section-spacing-y);
  padding-bottom: var(--section-spacing-y);
  margin-top: 0;
  margin-bottom: 0;
}

/* Tools section - standard spacing */
.toolsSection {
  padding-top: var(--section-spacing-y);
  padding-bottom: var(--section-spacing-y);
  margin-top: 0;
  margin-bottom: 0;
}

/* How It Works section - standard spacing */
.howItWorksSection {
  padding-top: var(--section-spacing-y);
  padding-bottom: var(--section-spacing-y);
  margin-top: 0;
  margin-bottom: 0;
}

/* Portfolio section - standard spacing */
.portfolioSection {
  padding-top: var(--section-spacing-y);
  padding-bottom: var(--section-spacing-y);
  margin-top: 0;
  margin-bottom: 0;
}

/* Stats section - remove any custom margins */
.statsSection {
  padding-top: var(--section-spacing-y);
  padding-bottom: var(--section-spacing-y);
  margin-top: 0 !important; /* Override any inline styles */
  margin-bottom: 0;
}

/* Testimonials section - standard spacing */
.testimonialsSection {
  padding-top: var(--section-spacing-y);
  padding-bottom: var(--section-spacing-y);
  margin-top: 0;
  margin-bottom: 0;
}

/* Pricing section - standard spacing */
.pricingSection {
  padding-top: var(--section-spacing-y);
  padding-bottom: var(--section-spacing-y);
  margin-top: 0;
  margin-bottom: 0;
}

/* FAQ section - standard spacing */
.faqSection {
  padding-top: var(--section-spacing-y);
  padding-bottom: var(--section-spacing-y);
  margin-top: 0;
  margin-bottom: 0;
}

/* Contact section - standard spacing */
.contactSection {
  padding-top: var(--section-spacing-y);
  padding-bottom: var(--section-spacing-y);
  margin-top: 0;
  margin-bottom: 0;
}

/* Footer - special case with less top padding */
.footer {
  padding-top: var(--section-spacing-y-sm);
  padding-bottom: var(--section-spacing-y-sm);
  margin-top: 0;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .heroSection {
    padding-bottom: var(--section-spacing-y-tablet);
  }
  
  .featuresSection,
  .toolsSection,
  .howItWorksSection,
  .portfolioSection,
  .statsSection,
  .testimonialsSection,
  .pricingSection,
  .faqSection,
  .contactSection {
    padding-top: var(--section-spacing-y-tablet);
    padding-bottom: var(--section-spacing-y-tablet);
  }
  
  .footer {
    padding-top: var(--section-spacing-y-mobile);
    padding-bottom: var(--section-spacing-y-mobile);
  }
}

@media (max-width: 768px) {
  .heroSection {
    padding-bottom: var(--section-spacing-y-mobile);
  }
  
  .featuresSection,
  .toolsSection,
  .howItWorksSection,
  .portfolioSection,
  .statsSection,
  .testimonialsSection,
  .pricingSection,
  .faqSection,
  .contactSection {
    padding-top: var(--section-spacing-y-mobile);
    padding-bottom: var(--section-spacing-y-mobile);
  }
  
  .footer {
    padding-top: var(--section-spacing-y-mobile-sm);
    padding-bottom: var(--section-spacing-y-mobile-sm);
  }
}

@media (max-width: 480px) {
  .heroSection {
    padding-bottom: var(--section-spacing-y-mobile-sm);
  }
  
  .featuresSection,
  .toolsSection,
  .howItWorksSection,
  .portfolioSection,
  .statsSection,
  .testimonialsSection,
  .pricingSection,
  .faqSection,
  .contactSection {
    padding-top: var(--section-spacing-y-mobile-sm);
    padding-bottom: var(--section-spacing-y-mobile-sm);
  }
  
  .footer {
    padding-top: calc(var(--section-spacing-y-mobile-sm) - 1rem);
    padding-bottom: calc(var(--section-spacing-y-mobile-sm) - 1rem);
  }
}
