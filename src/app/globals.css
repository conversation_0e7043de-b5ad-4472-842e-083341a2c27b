@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Import variables from the main variables file */
  @import "../styles/variables.css";

  /* Import standardized section spacing */
  @import "../styles/section-spacing.css";

  /* Layout - Standardized container widths */
  --container-max: 1200px;     /* Main container max width - standardized to 1200px */
  --container-narrow: 960px;   /* Narrower container for text-heavy sections */
  --container-text: 720px;     /* Container for pure text content */

  /* Additional variables specific to app/globals.css */
  --azure-blue-bg: rgba(68, 163, 219, 0.15);
  --blue-2-bg: rgba(13, 81, 127, 0.33);
  --elephant-grey: #3e3d49;
  --process-blue: #1343d3;
  --blue-3: #2969a2;
  --blue-4: #062057;
  --scroll-1-horiz: 0px;
}

body {
  margin: 0;
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-modern);
  color: var(--text-primary);
  line-height: 1.7; /* Improved line height for better readability */
  font-size: 16px;
  overflow-x: hidden;
  scroll-behavior: smooth;
  letter-spacing: 0.01em; /* Slight letter spacing for better readability */
  transition: background-color 0.3s ease;
}

/* Class applied when page is scrolled more than 20px */
body.is-scrolled {
  /* Add any styles you want to apply when the page is scrolled */
}

.body-wrapper {
  position: relative;
  overflow: hidden;
}

.body-wrapper.no-overflow {
  overflow: hidden;
}

.body-contents-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Standard container classes */
.container {
  width: 100%;
  max-width: var(--container-max);
  margin: 0 auto;
  padding-left: 2rem;
  padding-right: 2rem;
  position: relative;
  box-sizing: border-box;
}

.container-narrow {
  width: 100%;
  max-width: var(--container-narrow);
  margin: 0 auto;
  padding-left: 2rem;
  padding-right: 2rem;
  position: relative;
  box-sizing: border-box;
}

.container-text {
  width: 100%;
  max-width: var(--container-text);
  margin: 0 auto;
  padding-left: 2rem;
  padding-right: 2rem;
  position: relative;
  box-sizing: border-box;
}

.content {
  width: 100%;
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 6rem 2rem;
  position: relative;
  box-sizing: border-box;
}

/* Section spacing - standardized vertical rhythm */
.section {
  padding: var(--section-spacing-y) 0;
  position: relative;
  margin: 0; /* Reset margins to ensure consistent spacing */
}

.section-sm {
  padding: var(--section-spacing-y-sm) 0;
  position: relative;
  margin: 0;
}

.section-lg {
  padding: var(--section-spacing-y-lg) 0;
  position: relative;
  margin: 0;
}

/* Add a class for sections that need additional spacing */
.section-gap {
  margin-top: var(--section-spacing-gap);
}

/* Section heading organization */
.text-org {
  margin-bottom: var(--section-heading-spacing);
  text-align: center;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.text-org .subtitle {
  font-size: 1.25rem;
  color: var(--cw-2);
  margin-top: 1rem;
  line-height: 1.6;
  max-width: 650px;
  margin-left: auto;
  margin-right: auto;
}

.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  display: inline-block;
  font-weight: 800;
  font-size: 3.25rem; /* Slightly larger */
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
  line-height: 1.2;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); /* Subtle shadow for better readability */
  position: relative;
}

/* Add a subtle glow effect to headings */
.gradient-text::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  filter: blur(20px);
  opacity: 0.15;
  z-index: -1;
  background-image: inherit;
  border-radius: 50%;
}

.gradient-text.blue {
  background-image: var(--blue-gradient);
}

.gradient-text.green {
  background-image: var(--green-gradient);
}

.gradient-text.purple {
  background-image: var(--purple-gradient);
}

.gradient-text.yellow {
  background-image: var(--yellow-gradient);
}

.gradient-text.red {
  background-image: var(--red-gradient);
}

.gradient-text.doc-heading {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 2rem;
}

.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 1.1rem 2.2rem; /* Slightly larger padding */
  border-radius: 30px;
  background-color: rgba(255, 255, 255, var(--alpha-10));
  color: var(--text-primary);
  text-decoration: none;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1); /* Improved animation curve */
  font-weight: var(--font-semibold);
  font-size: 1.05rem; /* Slightly larger font */
  letter-spacing: 0.02em;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light); /* Subtle border */
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  background-color: rgba(255, 255, 255, var(--alpha-15));
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--color-green), var(--alpha-30));
}

.btn.hero {
  background-color: var(--accent-green);
  color: var(--color-black);
  box-shadow: 0 4px 20px rgba(var(--color-green), var(--alpha-30));
  font-weight: var(--font-bold); /* Bolder text for primary CTA */
  border: none;
  position: relative;
  overflow: hidden;
}

.btn.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, var(--alpha-20)), transparent);
  transition: 0.5s;
}

.btn.hero:hover::before {
  left: 100%;
}

.btn.hero:hover {
  box-shadow: 0 6px 25px rgba(var(--color-green), var(--alpha-40));
  background-color: rgba(var(--color-green), var(--alpha-90));
  transform: translateY(-3px) scale(1.02); /* Slightly larger on hover */
}

.btn.secondary {
  background-color: rgba(var(--color-azure), var(--alpha-20));
  color: var(--text-primary);
  border: 1px solid var(--color-azure);
}

.btn.secondary:hover {
  background-color: rgba(var(--color-azure), var(--alpha-30));
  box-shadow: 0 6px 25px rgba(var(--color-azure), var(--alpha-30));
}

.button-background {
  position: absolute;
  top: 0;
  left: -50%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, var(--alpha-20)), transparent);
  transition: transform 0.5s ease;
}

.button-background.hero {
  background: linear-gradient(90deg, transparent, var(--accent-green), transparent);
}

.button-text {
  position: relative;
  z-index: 1;
  margin-right: 0.5rem;
}

.icon.right {
  position: relative;
  z-index: 1;
  font-size: 0.9rem;
  transition: transform 0.3s ease;
}

.link {
  display: inline-flex;
  align-items: center;
  color: var(--text-cw-1);
  text-decoration: none;
  transition: color 0.2s ease;
}

.link:hover {
  color: var(--text-primary);
}

.badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: rgba(255, 255, 255, var(--alpha-10));
  border-radius: 20px;
  font-size: 0.9rem;
  color: var(--text-cw-3);
  margin-top: 1rem;
}

.doc-page {
  max-width: var(--container-text);
  padding: 6rem 2rem;
}

.rich-text-block {
  color: var(--text-cw-3);
  line-height: 1.6;
}

.rich-text-block h3 {
  color: var(--text-primary);
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.rich-text-block ul {
  margin-bottom: 1.5rem;
}

.rich-text-block p {
  margin-bottom: 1rem;
}

.animated-counter::after {
  content: '+';
}

/* Bootstrap icon spacing classes */
.me-1 {
  margin-right: 0.25rem !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.me-3 {
  margin-right: 1rem !important;
}

.ms-1 {
  margin-left: 0.25rem !important;
}

.ms-2 {
  margin-left: 0.5rem !important;
}

.ms-3 {
  margin-left: 1rem !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

/* Card styles */
.card {
  background-color: var(--bg-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--border-medium);
}

.card-title {
  font-size: 1.5rem;
  font-weight: var(--font-bold);
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.card-subtitle {
  font-size: 1.1rem;
  color: var(--text-cw-2);
  margin-bottom: 1.5rem;
}

.card-text {
  color: var(--text-cw-3);
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.card-featured {
  background: linear-gradient(to bottom right, rgba(var(--color-green), var(--alpha-05)), rgba(0, 0, 0, 0));
  border-color: var(--border-green);
}

.card-featured:hover {
  box-shadow: 0 8px 30px rgba(var(--color-green), var(--alpha-10));
}

/* Responsive breakpoints */

/* Large desktop styles */
@media (max-width: 1280px) {
  .container,
  .container-narrow,
  .container-text,
  .content {
    max-width: 100%;
  }
}

/* Tablet styles */
@media (max-width: 1024px) {
  .container,
  .container-narrow,
  .container-text,
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .content {
    padding-top: var(--section-spacing-y-tablet);
    padding-bottom: var(--section-spacing-y-tablet);
  }

  .section {
    padding: var(--section-spacing-y-tablet) 0;
  }

  .section-sm {
    padding: var(--section-spacing-y-mobile) 0;
  }

  .section-lg {
    padding: calc(var(--section-spacing-y-tablet) + 1rem) 0;
  }

  .gradient-text {
    font-size: 2.5rem;
  }

  /* Improve card layout on tablets */
  .card {
    padding: 1.25rem;
  }

  /* Adjust spacing between sections */
  .text-org {
    margin-bottom: var(--section-heading-spacing-mobile);
  }

  /* Adjust portfolio grid */
  .portfolio-grid {
    gap: 1.5rem;
  }
}

/* Mobile landscape styles */
@media (max-width: 768px) {
  .container,
  .container-narrow,
  .container-text,
  .content {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .content {
    padding-top: var(--section-spacing-y-mobile);
    padding-bottom: var(--section-spacing-y-mobile);
  }

  .section {
    padding: var(--section-spacing-y-mobile) 0;
  }

  .section-sm {
    padding: var(--section-spacing-y-mobile-sm) 0;
  }

  .section-lg {
    padding: var(--section-spacing-y-mobile) 0;
  }

  /* Adjust section gap for mobile */
  .section-gap {
    margin-top: calc(var(--section-spacing-gap) * 0.75);
  }

  .gradient-text {
    font-size: 2.2rem;
  }

  .gradient-text.doc-heading {
    font-size: 2.2rem;
  }

  .btn {
    padding: 0.9rem 1.8rem;
    font-size: 0.95rem;
  }

  /* Improve text readability on mobile */
  .text-org {
    margin-bottom: var(--section-heading-spacing-mobile);
  }

  .text-org .subtitle {
    font-size: 1.15rem;
    line-height: 1.7;
  }

  /* Adjust card spacing */
  .card {
    padding: 1.25rem;
    margin-bottom: 1.5rem;
  }

  .card-title {
    font-size: 1.3rem;
  }

  /* Improve portfolio grid on mobile */
  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  /* Adjust pricing cards */
  .pricing-card {
    padding: 1.5rem;
  }

  /* Improve footer readability */
  .footer-link {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }
}

/* Mobile portrait styles */
@media (max-width: 480px) {
  .container,
  .container-narrow,
  .container-text,
  .content {
    padding-left: 1.2rem;
    padding-right: 1.2rem;
  }

  .content {
    padding-top: var(--section-spacing-y-mobile-sm);
    padding-bottom: var(--section-spacing-y-mobile-sm);
  }

  .section {
    padding: var(--section-spacing-y-mobile-sm) 0;
  }

  .section-sm {
    padding: calc(var(--section-spacing-y-mobile-sm) - 0.5rem) 0;
  }

  .section-lg {
    padding: var(--section-spacing-y-mobile-sm) 0;
  }

  /* Further reduce section gap for small mobile */
  .section-gap {
    margin-top: calc(var(--section-spacing-gap) * 0.5);
  }

  .gradient-text {
    font-size: 2rem;
  }

  .gradient-text.doc-heading {
    font-size: 2rem;
  }

  .btn {
    padding: 0.8rem 1.6rem;
    font-size: 0.9rem;
    width: 100%;
    justify-content: center;
  }

  .text-org {
    margin-bottom: calc(var(--section-heading-spacing-mobile) * 0.75);
  }

  /* Improve card readability on small screens */
  .card {
    padding: 1.2rem;
  }

  .card-title {
    font-size: 1.2rem;
    margin-bottom: 0.75rem;
  }

  .card-subtitle {
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  /* Improve list readability */
  .pricing-feature {
    margin-bottom: 0.75rem;
  }

  /* Adjust testimonial cards */
  .testimonial-card {
    padding: 1rem;
  }

  /* Improve footer spacing */
  .footer-block {
    margin-bottom: 1.5rem;
  }

  .footer-link {
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
  }

  /* Ensure buttons are properly sized */
  .btn.hero, .btn.secondary {
    width: 100%;
    text-align: center;
    justify-content: center;
  }
}
